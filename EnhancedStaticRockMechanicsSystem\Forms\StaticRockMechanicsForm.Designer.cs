﻿namespace BritSystem
{
    partial class StaticRockMechanicsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            System.Windows.Forms.DataVisualization.Charting.ChartArea chartArea1 = new System.Windows.Forms.DataVisualization.Charting.ChartArea();
            System.Windows.Forms.DataVisualization.Charting.Legend legend1 = new System.Windows.Forms.DataVisualization.Charting.Legend();
            lblTitle = new Label();
            lblWelcome = new Label();
            btnBack = new Button();
            btnLogout = new Button();
            btnEmergencyExit = new Button();
            btnViewComparison = new Button();
            pnlParameters = new Panel();
            grpVs = new GroupBox();
            rbVelocityDts = new RadioButton();
            rbVelocityVs = new RadioButton();
            grpVp = new GroupBox();
            rbVelocityDt = new RadioButton();
            rbVelocityVp = new RadioButton();
            grpDensity = new GroupBox();
            rbDensityRhob = new RadioButton();
            rbDensityRho = new RadioButton();
            lblCalculationResult = new Label();
            btnCalculate = new Button();
            txtVs = new TextBox();
            lblVs = new Label();
            txtVp = new TextBox();
            lblVp = new Label();
            txtDensity = new TextBox();
            lblDensity = new Label();
            lblParametersTitle = new Label();
            pnlData = new Panel();
            dgvMechanicsData = new DataGridView();
            btnExport = new Button();
            btnImport = new Button();
            lblDataTitle = new Label();
            pnlChart = new Panel();
            chartBrittleness = new System.Windows.Forms.DataVisualization.Charting.Chart();
            btnSaveCurve = new Button();
            btnReset = new Button();
            btnGenerateCurve = new Button();
            lblChartTitle = new Label();
            pnlParameters.SuspendLayout();
            grpVs.SuspendLayout();
            grpVp.SuspendLayout();
            grpDensity.SuspendLayout();
            pnlData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvMechanicsData).BeginInit();
            pnlChart.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)chartBrittleness).BeginInit();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.BackColor = Color.FromArgb(45, 45, 45);
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Font = new Font("微软雅黑", 18F, FontStyle.Bold, GraphicsUnit.Point, 134);
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.Location = new Point(0, 0);
            lblTitle.Margin = new Padding(6, 0, 6, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(2564, 120);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "静态岩石力学参数法脆性指数计算系统";
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblWelcome
            // 
            lblWelcome.AutoSize = true;
            lblWelcome.Font = new Font("微软雅黑", 12F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblWelcome.ForeColor = Color.LightGray;
            lblWelcome.Location = new Point(37, 140);
            lblWelcome.Margin = new Padding(6, 0, 6, 0);
            lblWelcome.Name = "lblWelcome";
            lblWelcome.Size = new Size(326, 31);
            lblWelcome.TabIndex = 1;
            lblWelcome.Text = "欢迎使用静态岩石力学参数法";
            // 
            // btnBack
            // 
            btnBack.BackColor = Color.FromArgb(50, 50, 50);
            btnBack.FlatAppearance.BorderColor = Color.Cyan;
            btnBack.FlatStyle = FlatStyle.Flat;
            btnBack.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnBack.ForeColor = Color.LightSkyBlue;
            btnBack.Location = new Point(37, 220);
            btnBack.Margin = new Padding(6);
            btnBack.Name = "btnBack";
            btnBack.Size = new Size(220, 70);
            btnBack.TabIndex = 2;
            btnBack.Text = "返回主页";
            btnBack.UseVisualStyleBackColor = false;
            btnBack.Click += BtnBack_Click;
            // 
            // btnLogout
            // 
            btnLogout.BackColor = Color.FromArgb(50, 50, 50);
            btnLogout.FlatAppearance.BorderColor = Color.Cyan;
            btnLogout.FlatStyle = FlatStyle.Flat;
            btnLogout.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnLogout.ForeColor = Color.LightSkyBlue;
            btnLogout.Location = new Point(293, 220);
            btnLogout.Margin = new Padding(6);
            btnLogout.Name = "btnLogout";
            btnLogout.Size = new Size(220, 70);
            btnLogout.TabIndex = 3;
            btnLogout.Text = "退出登录";
            btnLogout.UseVisualStyleBackColor = false;
            btnLogout.Click += BtnLogout_Click;
            // 
            // btnEmergencyExit
            // 
            btnEmergencyExit.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnEmergencyExit.BackColor = Color.FromArgb(80, 30, 30);
            btnEmergencyExit.FlatAppearance.BorderColor = Color.Red;
            btnEmergencyExit.FlatStyle = FlatStyle.Flat;
            btnEmergencyExit.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnEmergencyExit.ForeColor = Color.White;
            btnEmergencyExit.Location = new Point(2325, 24);
            btnEmergencyExit.Margin = new Padding(6);
            btnEmergencyExit.Name = "btnEmergencyExit";
            btnEmergencyExit.Size = new Size(211, 60);
            btnEmergencyExit.TabIndex = 4;
            btnEmergencyExit.Text = "存为对比图";
            btnEmergencyExit.UseVisualStyleBackColor = false;
            btnEmergencyExit.Click += BtnEmergencyExit_Click;
            //
            // btnViewComparison
            //
            btnViewComparison.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnViewComparison.BackColor = Color.FromArgb(50, 50, 50);
            btnViewComparison.FlatAppearance.BorderColor = Color.Cyan;
            btnViewComparison.FlatStyle = FlatStyle.Flat;
            btnViewComparison.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnViewComparison.ForeColor = Color.LightSkyBlue;
            btnViewComparison.Location = new Point(549, 220);
            btnViewComparison.Margin = new Padding(6);
            btnViewComparison.Name = "btnViewComparison";
            btnViewComparison.Size = new Size(220, 70);
            btnViewComparison.TabIndex = 5;
            btnViewComparison.Text = "查看对比图";
            btnViewComparison.UseVisualStyleBackColor = false;
            btnViewComparison.Click += BtnViewComparison_Click;
            // 
            // pnlParameters
            // 
            pnlParameters.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pnlParameters.BackColor = Color.FromArgb(45, 45, 45);
            pnlParameters.BorderStyle = BorderStyle.FixedSingle;
            pnlParameters.Controls.Add(grpVs);
            pnlParameters.Controls.Add(grpVp);
            pnlParameters.Controls.Add(grpDensity);
            pnlParameters.Controls.Add(lblCalculationResult);
            pnlParameters.Controls.Add(btnCalculate);
            pnlParameters.Controls.Add(txtVs);
            pnlParameters.Controls.Add(lblVs);
            pnlParameters.Controls.Add(txtVp);
            pnlParameters.Controls.Add(lblVp);
            pnlParameters.Controls.Add(txtDensity);
            pnlParameters.Controls.Add(lblDensity);
            pnlParameters.Controls.Add(lblParametersTitle);
            pnlParameters.Location = new Point(37, 320);
            pnlParameters.Margin = new Padding(6);
            pnlParameters.Name = "pnlParameters";
            pnlParameters.Size = new Size(2490, 238);
            pnlParameters.TabIndex = 5;
            // 
            // grpVs
            // 
            grpVs.Controls.Add(rbVelocityDts);
            grpVs.Controls.Add(rbVelocityVs);
            grpVs.ForeColor = Color.WhiteSmoke;
            grpVs.Location = new Point(2184, 63);
            grpVs.Name = "grpVs";
            grpVs.Size = new Size(271, 150);
            grpVs.TabIndex = 10;
            grpVs.TabStop = false;
            grpVs.Text = "横波速度单位";
            // 
            // rbVelocityDts
            // 
            rbVelocityDts.AutoSize = true;
            rbVelocityDts.Location = new Point(93, 101);
            rbVelocityDts.Name = "rbVelocityDts";
            rbVelocityDts.Size = new Size(130, 28);
            rbVelocityDts.TabIndex = 5;
            rbVelocityDts.TabStop = true;
            rbVelocityDts.Text = "DTS (μs/m)";
            rbVelocityDts.UseVisualStyleBackColor = true;
            // 
            // rbVelocityVs
            // 
            rbVelocityVs.AutoSize = true;
            rbVelocityVs.Location = new Point(95, 49);
            rbVelocityVs.Name = "rbVelocityVs";
            rbVelocityVs.Size = new Size(105, 28);
            rbVelocityVs.TabIndex = 4;
            rbVelocityVs.TabStop = true;
            rbVelocityVs.Text = "Vs (m/s)";
            rbVelocityVs.UseVisualStyleBackColor = true;
            // 
            // grpVp
            // 
            grpVp.Controls.Add(rbVelocityDt);
            grpVp.Controls.Add(rbVelocityVp);
            grpVp.ForeColor = Color.WhiteSmoke;
            grpVp.Location = new Point(1846, 59);
            grpVp.Name = "grpVp";
            grpVp.Size = new Size(283, 150);
            grpVp.TabIndex = 10;
            grpVp.TabStop = false;
            grpVp.Text = "纵波速度单位";
            // 
            // rbVelocityDt
            // 
            rbVelocityDt.AutoSize = true;
            rbVelocityDt.Location = new Point(79, 101);
            rbVelocityDt.Name = "rbVelocityDt";
            rbVelocityDt.Size = new Size(132, 28);
            rbVelocityDt.TabIndex = 3;
            rbVelocityDt.TabStop = true;
            rbVelocityDt.Text = "DT (μs/m）";
            rbVelocityDt.UseVisualStyleBackColor = true;
            // 
            // rbVelocityVp
            // 
            rbVelocityVp.AutoSize = true;
            rbVelocityVp.Location = new Point(81, 49);
            rbVelocityVp.Name = "rbVelocityVp";
            rbVelocityVp.Size = new Size(109, 28);
            rbVelocityVp.TabIndex = 2;
            rbVelocityVp.TabStop = true;
            rbVelocityVp.Text = "Vp (m/s)";
            rbVelocityVp.UseVisualStyleBackColor = true;
            // 
            // grpDensity
            // 
            grpDensity.BackColor = Color.FromArgb(50, 50, 50);
            grpDensity.Controls.Add(rbDensityRhob);
            grpDensity.Controls.Add(rbDensityRho);
            grpDensity.ForeColor = Color.WhiteSmoke;
            grpDensity.Location = new Point(1505, 59);
            grpDensity.Name = "grpDensity";
            grpDensity.Size = new Size(277, 150);
            grpDensity.TabIndex = 9;
            grpDensity.TabStop = false;
            grpDensity.Text = "密度单位";
            // 
            // rbDensityRhob
            // 
            rbDensityRhob.AutoSize = true;
            rbDensityRhob.Location = new Point(64, 101);
            rbDensityRhob.Name = "rbDensityRhob";
            rbDensityRhob.Size = new Size(157, 28);
            rbDensityRhob.TabIndex = 1;
            rbDensityRhob.TabStop = true;
            rbDensityRhob.Text = "RHOB (g/cm³)";
            rbDensityRhob.UseVisualStyleBackColor = true;
            // 
            // rbDensityRho
            // 
            rbDensityRho.AutoSize = true;
            rbDensityRho.Location = new Point(69, 49);
            rbDensityRho.Name = "rbDensityRho";
            rbDensityRho.Size = new Size(116, 28);
            rbDensityRho.TabIndex = 0;
            rbDensityRho.TabStop = true;
            rbDensityRho.Text = "ρ (g/cm³)";
            rbDensityRho.UseVisualStyleBackColor = true;
            // 
            // lblCalculationResult
            // 
            lblCalculationResult.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblCalculationResult.ForeColor = Color.Yellow;
            lblCalculationResult.Location = new Point(293, 170);
            lblCalculationResult.Margin = new Padding(6, 0, 6, 0);
            lblCalculationResult.Name = "lblCalculationResult";
            lblCalculationResult.Size = new Size(1100, 60);
            lblCalculationResult.TabIndex = 8;
            lblCalculationResult.Text = "计算结果将在此显示";
            lblCalculationResult.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // btnCalculate
            // 
            btnCalculate.BackColor = Color.FromArgb(50, 50, 50);
            btnCalculate.FlatAppearance.BorderColor = Color.Cyan;
            btnCalculate.FlatStyle = FlatStyle.Flat;
            btnCalculate.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnCalculate.ForeColor = Color.LightSkyBlue;
            btnCalculate.Location = new Point(25, 170);
            btnCalculate.Margin = new Padding(6);
            btnCalculate.Name = "btnCalculate";
            btnCalculate.Size = new Size(220, 60);
            btnCalculate.TabIndex = 7;
            btnCalculate.Text = "计算脆性指数";
            btnCalculate.UseVisualStyleBackColor = false;
            btnCalculate.Click += BtnCalculate_Click;
            // 
            // txtVs
            // 
            txtVs.BackColor = Color.FromArgb(60, 60, 60);
            txtVs.BorderStyle = BorderStyle.FixedSingle;
            txtVs.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            txtVs.ForeColor = Color.White;
            txtVs.Location = new Point(1111, 100);
            txtVs.Margin = new Padding(6);
            txtVs.Name = "txtVs";
            txtVs.Size = new Size(166, 34);
            txtVs.TabIndex = 6;
            // 
            // lblVs
            // 
            lblVs.AutoSize = true;
            lblVs.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblVs.ForeColor = Color.White;
            lblVs.Location = new Point(899, 104);
            lblVs.Margin = new Padding(6, 0, 6, 0);
            lblVs.Name = "lblVs";
            lblVs.Size = new Size(183, 27);
            lblVs.TabIndex = 5;
            lblVs.Text = "横波速度 Vs (m/s):";
            // 
            // txtVp
            // 
            txtVp.BackColor = Color.FromArgb(60, 60, 60);
            txtVp.BorderStyle = BorderStyle.FixedSingle;
            txtVp.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            txtVp.ForeColor = Color.White;
            txtVp.Location = new Point(659, 100);
            txtVp.Margin = new Padding(6);
            txtVp.Name = "txtVp";
            txtVp.Size = new Size(187, 34);
            txtVp.TabIndex = 4;
            // 
            // lblVp
            // 
            lblVp.AutoSize = true;
            lblVp.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblVp.ForeColor = Color.White;
            lblVp.Location = new Point(449, 104);
            lblVp.Margin = new Padding(6, 0, 6, 0);
            lblVp.Name = "lblVp";
            lblVp.Size = new Size(187, 27);
            lblVp.TabIndex = 3;
            lblVp.Text = "纵波速度 Vp (m/s):";
            // 
            // txtDensity
            // 
            txtDensity.BackColor = Color.FromArgb(60, 60, 60);
            txtDensity.BorderStyle = BorderStyle.FixedSingle;
            txtDensity.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            txtDensity.ForeColor = Color.White;
            txtDensity.Location = new Point(258, 100);
            txtDensity.Margin = new Padding(6);
            txtDensity.Name = "txtDensity";
            txtDensity.Size = new Size(164, 34);
            txtDensity.TabIndex = 2;
            // 
            // lblDensity
            // 
            lblDensity.AutoSize = true;
            lblDensity.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblDensity.ForeColor = Color.White;
            lblDensity.Location = new Point(20, 104);
            lblDensity.Margin = new Padding(6, 0, 6, 0);
            lblDensity.Name = "lblDensity";
            lblDensity.Size = new Size(195, 27);
            lblDensity.TabIndex = 1;
            lblDensity.Text = "岩石密度 ρ (g/cm³):";
            // 
            // lblParametersTitle
            // 
            lblParametersTitle.AutoSize = true;
            lblParametersTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            lblParametersTitle.ForeColor = Color.White;
            lblParametersTitle.Location = new Point(18, 20);
            lblParametersTitle.Margin = new Padding(6, 0, 6, 0);
            lblParametersTitle.Name = "lblParametersTitle";
            lblParametersTitle.Size = new Size(206, 31);
            lblParametersTitle.TabIndex = 0;
            lblParametersTitle.Text = "岩石力学参数输入";
            // 
            // pnlData
            // 
            pnlData.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left;
            pnlData.BackColor = Color.FromArgb(45, 45, 45);
            pnlData.BorderStyle = BorderStyle.FixedSingle;
            pnlData.Controls.Add(dgvMechanicsData);
            pnlData.Controls.Add(btnExport);
            pnlData.Controls.Add(btnImport);
            pnlData.Controls.Add(lblDataTitle);
            pnlData.Location = new Point(37, 600);
            pnlData.Margin = new Padding(6);
            pnlData.Name = "pnlData";
            pnlData.Size = new Size(1098, 710);
            pnlData.TabIndex = 6;
            // 
            // dgvMechanicsData
            // 
            dgvMechanicsData.AllowUserToAddRows = false;
            dgvMechanicsData.AllowUserToDeleteRows = false;
            dgvMechanicsData.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dgvMechanicsData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvMechanicsData.BackgroundColor = Color.FromArgb(60, 60, 60);
            dgvMechanicsData.BorderStyle = BorderStyle.None;
            dataGridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(80, 80, 80);
            dataGridViewCellStyle1.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle1.ForeColor = Color.White;
            dataGridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = DataGridViewTriState.True;
            dgvMechanicsData.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            dgvMechanicsData.ColumnHeadersHeight = 34;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = Color.FromArgb(60, 60, 60);
            dataGridViewCellStyle2.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(100, 100, 100);
            dataGridViewCellStyle2.SelectionForeColor = Color.White;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.False;
            dgvMechanicsData.DefaultCellStyle = dataGridViewCellStyle2;
            dgvMechanicsData.EnableHeadersVisualStyles = false;
            dgvMechanicsData.GridColor = Color.FromArgb(80, 80, 80);
            dgvMechanicsData.Location = new Point(18, 100);
            dgvMechanicsData.Margin = new Padding(6);
            dgvMechanicsData.Name = "dgvMechanicsData";
            dgvMechanicsData.ReadOnly = true;
            dgvMechanicsData.RowHeadersWidth = 62;
            dgvMechanicsData.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvMechanicsData.Size = new Size(1060, 588);
            dgvMechanicsData.TabIndex = 3;
            // 
            // btnExport
            // 
            btnExport.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnExport.BackColor = Color.FromArgb(50, 50, 50);
            btnExport.FlatAppearance.BorderColor = Color.Cyan;
            btnExport.FlatStyle = FlatStyle.Flat;
            btnExport.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnExport.ForeColor = Color.LightSkyBlue;
            btnExport.Location = new Point(895, 20);
            btnExport.Margin = new Padding(6);
            btnExport.Name = "btnExport";
            btnExport.Size = new Size(183, 60);
            btnExport.TabIndex = 2;
            btnExport.Text = "导出数据";
            btnExport.UseVisualStyleBackColor = false;
            btnExport.Click += BtnExport_Click;
            // 
            // btnImport
            // 
            btnImport.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnImport.BackColor = Color.FromArgb(50, 50, 50);
            btnImport.FlatAppearance.BorderColor = Color.Cyan;
            btnImport.FlatStyle = FlatStyle.Flat;
            btnImport.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnImport.ForeColor = Color.LightSkyBlue;
            btnImport.Location = new Point(693, 20);
            btnImport.Margin = new Padding(6);
            btnImport.Name = "btnImport";
            btnImport.Size = new Size(183, 60);
            btnImport.TabIndex = 1;
            btnImport.Text = "导入数据";
            btnImport.UseVisualStyleBackColor = false;
            btnImport.Click += BtnImport_Click;
            // 
            // lblDataTitle
            // 
            lblDataTitle.AutoSize = true;
            lblDataTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            lblDataTitle.ForeColor = Color.White;
            lblDataTitle.Location = new Point(18, 20);
            lblDataTitle.Margin = new Padding(6, 0, 6, 0);
            lblDataTitle.Name = "lblDataTitle";
            lblDataTitle.Size = new Size(206, 31);
            lblDataTitle.TabIndex = 0;
            lblDataTitle.Text = "岩石力学参数数据";
            // 
            // pnlChart
            //
            pnlChart.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            pnlChart.BackColor = Color.FromArgb(45, 45, 45);
            pnlChart.BorderStyle = BorderStyle.FixedSingle;
            pnlChart.Controls.Add(chartBrittleness);
            pnlChart.Controls.Add(btnSaveCurve);
            pnlChart.Controls.Add(btnReset);
            pnlChart.Controls.Add(btnGenerateCurve);
            pnlChart.Controls.Add(lblChartTitle);
            pnlChart.Location = new Point(1173, 600);
            pnlChart.Margin = new Padding(6);
            pnlChart.Name = "pnlChart";
            pnlChart.Size = new Size(1354, 710);
            pnlChart.TabIndex = 7;
            // 
            // btnSaveCurve
            // 
            btnSaveCurve.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnSaveCurve.BackColor = Color.FromArgb(50, 50, 50);
            btnSaveCurve.FlatAppearance.BorderColor = Color.Cyan;
            btnSaveCurve.FlatStyle = FlatStyle.Flat;
            btnSaveCurve.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnSaveCurve.ForeColor = Color.LightSkyBlue;
            btnSaveCurve.Location = new Point(1150, 20);
            btnSaveCurve.Margin = new Padding(6);
            btnSaveCurve.Name = "btnSaveCurve";
            btnSaveCurve.Size = new Size(183, 60);
            btnSaveCurve.TabIndex = 3;
            btnSaveCurve.Text = "保存曲线";
            btnSaveCurve.UseVisualStyleBackColor = false;
            btnSaveCurve.Click += BtnSaveCurve_Click;
            // 
            // btnReset
            // 
            btnReset.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnReset.BackColor = Color.FromArgb(50, 50, 50);
            btnReset.FlatAppearance.BorderColor = Color.Cyan;
            btnReset.FlatStyle = FlatStyle.Flat;
            btnReset.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnReset.ForeColor = Color.LightSkyBlue;
            btnReset.Location = new Point(949, 20);
            btnReset.Margin = new Padding(6);
            btnReset.Name = "btnReset";
            btnReset.Size = new Size(183, 60);
            btnReset.TabIndex = 2;
            btnReset.Text = "重置";
            btnReset.UseVisualStyleBackColor = false;
            btnReset.Click += BtnReset_Click;
            // 
            // btnGenerateCurve
            // 
            btnGenerateCurve.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnGenerateCurve.BackColor = Color.FromArgb(50, 50, 50);
            btnGenerateCurve.FlatAppearance.BorderColor = Color.Cyan;
            btnGenerateCurve.FlatStyle = FlatStyle.Flat;
            btnGenerateCurve.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnGenerateCurve.ForeColor = Color.LightSkyBlue;
            btnGenerateCurve.Location = new Point(747, 20);
            btnGenerateCurve.Margin = new Padding(6);
            btnGenerateCurve.Name = "btnGenerateCurve";
            btnGenerateCurve.Size = new Size(183, 60);
            btnGenerateCurve.TabIndex = 1;
            btnGenerateCurve.Text = "生成曲线";
            btnGenerateCurve.UseVisualStyleBackColor = false;
            btnGenerateCurve.Click += BtnGenerateCurve_Click;
            //
            // chartBrittleness
            //
            chartArea1.AxisX.Title = "脆性指数 (%)";
            chartArea1.AxisY.Title = "深度 (m)";
            chartArea1.AxisY.IsReversed = true;
            chartArea1.BackColor = Color.FromArgb(60, 60, 60);
            chartArea1.Name = "ChartArea1";
            chartBrittleness.ChartAreas.Add(chartArea1);
            legend1.BackColor = Color.FromArgb(60, 60, 60);
            legend1.ForeColor = Color.White;
            legend1.Name = "Legend1";
            chartBrittleness.Legends.Add(legend1);
            chartBrittleness.Location = new Point(18, 100);
            chartBrittleness.Margin = new Padding(6);
            chartBrittleness.Name = "chartBrittleness";
            chartBrittleness.Size = new Size(1316, 588);
            chartBrittleness.TabIndex = 4;
            chartBrittleness.Text = "脆性指数曲线";
            chartBrittleness.BackColor = Color.FromArgb(60, 60, 60);
            chartBrittleness.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            //
            // lblChartTitle
            //
            lblChartTitle.AutoSize = true;
            lblChartTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            lblChartTitle.ForeColor = Color.White;
            lblChartTitle.Location = new Point(18, 20);
            lblChartTitle.Margin = new Padding(6, 0, 6, 0);
            lblChartTitle.Name = "lblChartTitle";
            lblChartTitle.Size = new Size(182, 31);
            lblChartTitle.TabIndex = 0;
            lblChartTitle.Text = "脆性指数曲线图";
            // 
            // StaticRockMechanicsForm
            // 
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(33, 33, 33);
            ClientSize = new Size(2564, 1410);
            Controls.Add(pnlChart);
            Controls.Add(pnlData);
            Controls.Add(pnlParameters);
            Controls.Add(btnViewComparison);
            Controls.Add(btnEmergencyExit);
            Controls.Add(btnLogout);
            Controls.Add(btnBack);
            Controls.Add(lblWelcome);
            Controls.Add(lblTitle);
            ForeColor = Color.White;
            Margin = new Padding(6);
            Name = "StaticRockMechanicsForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "静态岩石力学参数法 - 脆性指数计算";
            WindowState = FormWindowState.Maximized;
            pnlParameters.ResumeLayout(false);
            pnlParameters.PerformLayout();
            grpVs.ResumeLayout(false);
            grpVs.PerformLayout();
            grpVp.ResumeLayout(false);
            grpVp.PerformLayout();
            grpDensity.ResumeLayout(false);
            grpDensity.PerformLayout();
            pnlData.ResumeLayout(false);
            pnlData.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgvMechanicsData).EndInit();
            pnlChart.ResumeLayout(false);
            pnlChart.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)chartBrittleness).EndInit();
            ResumeLayout(false);
            PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Label lblWelcome;
        private System.Windows.Forms.Button btnBack;
        private System.Windows.Forms.Button btnLogout;
        private System.Windows.Forms.Button btnEmergencyExit;
        private System.Windows.Forms.Button btnViewComparison;
        private System.Windows.Forms.Panel pnlParameters;
        private System.Windows.Forms.Label lblCalculationResult;
        private System.Windows.Forms.Button btnCalculate;
        private System.Windows.Forms.TextBox txtVs;
        private System.Windows.Forms.Label lblVs;
        private System.Windows.Forms.TextBox txtVp;
        private System.Windows.Forms.Label lblVp;
        private System.Windows.Forms.TextBox txtDensity;
        private System.Windows.Forms.Label lblDensity;
        private System.Windows.Forms.Label lblParametersTitle;
        private System.Windows.Forms.Panel pnlData;
        private System.Windows.Forms.DataGridView dgvMechanicsData;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.Button btnImport;
        private System.Windows.Forms.Label lblDataTitle;
        private System.Windows.Forms.Panel pnlChart;
        private System.Windows.Forms.DataVisualization.Charting.Chart chartBrittleness;
        private System.Windows.Forms.Button btnSaveCurve;
        private System.Windows.Forms.Button btnReset;
        private System.Windows.Forms.Button btnGenerateCurve;
        private System.Windows.Forms.Label lblChartTitle;
        private GroupBox grpDensity;
        private GroupBox grpVs;
        private GroupBox grpVp;
        private RadioButton rbVelocityDts;
        private RadioButton rbVelocityVs;
        private RadioButton rbVelocityDt;
        private RadioButton rbVelocityVp;
        private RadioButton rbDensityRhob;
        private RadioButton rbDensityRho;
    }
}