using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using EnhancedStaticRockMechanicsSystem.Services;

namespace EnhancedStaticRockMechanicsSystem.Forms
{
    /// <summary>
    /// 批量导入向导窗体
    /// </summary>
    public partial class BatchImportWizard : Form
    {
        private ListBox lstFiles;
        private Button btnAddFiles;
        private Button btnRemoveFiles;
        private Button btnPreview;
        private DataGridView dgvPreview;
        private Button btnOK;
        private Button btnCancel;
        private Label lblInstructions;
        private ProgressBar progressBar;

        public string[] SelectedFiles => lstFiles.Items.Cast<string>().ToArray();

        public BatchImportWizard()
        {
            InitializeComponent();
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            this.Text = "批量导入对比数据向导";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.FromArgb(33, 33, 33);
            this.ForeColor = Color.White;

            // 说明标签
            lblInstructions = new Label
            {
                Text = "选择要导入的对比数据文件。支持Excel (.xlsx/.xls)、CSV (.csv) 和 JSON (.json) 格式。",
                Location = new Point(10, 10),
                Size = new Size(860, 40),
                ForeColor = Color.LightGray,
                Font = new Font("微软雅黑", 9F)
            };

            // 文件列表
            lstFiles = new ListBox
            {
                Location = new Point(10, 60),
                Size = new Size(400, 450),
                SelectionMode = SelectionMode.MultiExtended,
                BackColor = Color.FromArgb(45, 45, 45),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // 按钮面板
            var btnPanel = new Panel
            {
                Location = new Point(420, 60),
                Size = new Size(100, 450),
                BackColor = Color.Transparent
            };

            btnAddFiles = new Button
            {
                Text = "添加文件",
                Location = new Point(10, 10),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnAddFiles.Click += BtnAddFiles_Click;

            btnRemoveFiles = new Button
            {
                Text = "移除文件",
                Location = new Point(10, 55),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnRemoveFiles.Click += BtnRemoveFiles_Click;

            btnPreview = new Button
            {
                Text = "预览数据",
                Location = new Point(10, 100),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnPreview.Click += BtnPreview_Click;

            btnPanel.Controls.AddRange(new Control[] { btnAddFiles, btnRemoveFiles, btnPreview });

            // 预览表格
            dgvPreview = new DataGridView
            {
                Location = new Point(530, 60),
                Size = new Size(350, 450),
                ReadOnly = true,
                AllowUserToAddRows = false,
                BackgroundColor = Color.FromArgb(45, 45, 45),
                GridColor = Color.FromArgb(60, 60, 60),
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(45, 45, 45),
                    ForeColor = Color.White,
                    SelectionBackColor = Color.FromArgb(0, 120, 215),
                    SelectionForeColor = Color.White
                },
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(60, 60, 60),
                    ForeColor = Color.White,
                    Font = new Font("微软雅黑", 9F, FontStyle.Bold)
                },
                EnableHeadersVisualStyles = false,
                BorderStyle = BorderStyle.FixedSingle
            };

            // 进度条
            progressBar = new ProgressBar
            {
                Location = new Point(10, 520),
                Size = new Size(870, 20),
                Style = ProgressBarStyle.Continuous,
                Visible = false
            };

            // 确定取消按钮
            btnOK = new Button
            {
                Text = "确定导入",
                Location = new Point(700, 550),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.OK,
                Enabled = false
            };

            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(800, 550),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.Cancel
            };

            // 添加控件
            this.Controls.AddRange(new Control[] {
                lblInstructions, lstFiles, btnPanel, dgvPreview,
                progressBar, btnOK, btnCancel
            });

            // 设置按钮样式
            SetButtonStyle(btnAddFiles);
            SetButtonStyle(btnRemoveFiles);
            SetButtonStyle(btnPreview);
            SetButtonStyle(btnOK);
            SetButtonStyle(btnCancel);
        }

        private void SetButtonStyle(Button button)
        {
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = Color.FromArgb(100, 100, 100);
            button.FlatAppearance.MouseOverBackColor = Color.FromArgb(80, 80, 80);
            button.FlatAppearance.MouseDownBackColor = Color.FromArgb(40, 40, 40);
        }

        private void BtnAddFiles_Click(object sender, EventArgs e)
        {
            var manager = new UnifiedComparisonDataManager();
            var files = manager.ShowSmartFileDialog();

            foreach (var file in files)
            {
                if (!lstFiles.Items.Contains(file))
                {
                    lstFiles.Items.Add(file);
                }
            }

            UpdateUI();
        }

        private void BtnRemoveFiles_Click(object sender, EventArgs e)
        {
            var selectedItems = lstFiles.SelectedItems.Cast<string>().ToArray();
            foreach (var item in selectedItems)
            {
                lstFiles.Items.Remove(item);
            }

            UpdateUI();
        }

        private async void BtnPreview_Click(object sender, EventArgs e)
        {
            if (lstFiles.Items.Count == 0) return;

            try
            {
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                btnPreview.Enabled = false;

                var manager = new UnifiedComparisonDataManager();
                var dataSets = await manager.LoadComparisonDataFromMultipleSources(SelectedFiles);

                // 创建预览数据表
                var previewTable = new DataTable();
                previewTable.Columns.Add("系统名称", typeof(string));
                previewTable.Columns.Add("文件名", typeof(string));
                previewTable.Columns.Add("数据点数", typeof(int));
                previewTable.Columns.Add("关联图片数", typeof(int));
                previewTable.Columns.Add("状态", typeof(string));

                foreach (var dataSet in dataSets)
                {
                    previewTable.Rows.Add(
                        dataSet.SystemName,
                        System.IO.Path.GetFileName(dataSet.DataSource),
                        dataSet.DataPoints.Count,
                        dataSet.AssociatedImages.Count,
                        dataSet.IsValid() ? "有效" : "无效"
                    );
                }

                dgvPreview.DataSource = previewTable;

                // 调整列宽
                if (dgvPreview.Columns.Count > 0)
                {
                    dgvPreview.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);
                }

                btnOK.Enabled = dataSets.Count > 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"预览失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                progressBar.Visible = false;
                btnPreview.Enabled = true;
            }
        }

        private void UpdateUI()
        {
            btnRemoveFiles.Enabled = lstFiles.SelectedItems.Count > 0;
            btnPreview.Enabled = lstFiles.Items.Count > 0;

            if (lstFiles.Items.Count == 0)
            {
                dgvPreview.DataSource = null;
                btnOK.Enabled = false;
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            UpdateUI();
        }
    }
}
