using System;
using System.Windows.Forms;
using EnhancedStaticRockMechanicsSystem.Services;
using EnhancedStaticRockMechanicsSystem.Forms;

namespace EnhancedStaticRockMechanicsSystem
{
    /// <summary>
    /// 增强版静态岩石力学参数法系统主程序
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // 初始化日志服务
                LoggingService.Instance.Info("增强版静态岩石力学参数法系统启动");

                // 显示登录窗体
                LoginForm loginForm = new LoginForm();
                DialogResult loginResult = loginForm.ShowDialog();

                if (loginResult == DialogResult.OK)
                {
                    // 登录成功，显示主界面
                    string username = loginForm.Username;
                    LoggingService.Instance.Info($"用户 {username} 登录成功");

                    DashboardForm dashboardForm = new DashboardForm(username);
                    Application.Run(dashboardForm);
                }
                else
                {
                    // 登录失败或取消，退出程序
                    LoggingService.Instance.Info("用户取消登录，程序退出");
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"系统启动失败: {ex.Message}");
                MessageBox.Show($"系统启动失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
