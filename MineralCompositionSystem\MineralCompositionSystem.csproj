<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <!-- 禁用默认项目项 -->
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <!-- 禁用自动生成资源名称 -->
    <GenerateResourceMSBuildArchitecture>CurrentArchitecture</GenerateResourceMSBuildArchitecture>
    <GenerateResourceMSBuildRuntime>CurrentRuntime</GenerateResourceMSBuildRuntime>
    <!-- 强制使用显式指定的ManifestResourceName -->
    <EmbeddedResourceUseDependentUponConvention>false</EmbeddedResourceUseDependentUponConvention>
    <!-- 高DPI支持设置 -->
    <ApplicationHighDpiMode>PerMonitorV2</ApplicationHighDpiMode>
    <AssemblyTitle>矿物组分法脆性指数分析系统</AssemblyTitle>
    <Product>矿物组分法脆性指数分析系统</Product>
    <Description>基于矿物成分计算脆性指数的专业地质分析软件</Description>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <!-- 包引用 -->
  <ItemGroup>
    <PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
    <PackageReference Include="EPPlus" Version="8.0.1" />
    <PackageReference Include="HIC.System.Windows.Forms.DataVisualization" Version="1.0.1" />
    <PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.1774.30" />
    <PackageReference Include="Microsoft.Web.WebView2.DevToolsProtocolExtension" Version="1.0.824" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Data.OleDb" Version="9.0.4" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
  </ItemGroup>

  <!-- 源代码文件 -->
  <ItemGroup>
    <!-- 核心类 -->
    <Compile Include="Core\BrittlenessCalculator.cs" />
    <Compile Include="Core\ColumnDetector.cs" />
    <Compile Include="Core\DataManager.cs" />
    <Compile Include="Core\FormAdapter.cs" />
    <Compile Include="Core\AlgorithmFormulaCal.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Core\AlgorithmFormulaCal.Designer.cs">
      <DependentUpon>AlgorithmFormulaCal.cs</DependentUpon>
    </Compile>

    <!-- 模型类 -->
    <Compile Include="Models\BrittlenessDataPoint.cs" />
    <Compile Include="Models\MineralData.cs" />
    <Compile Include="Models\CalculationResult.cs" />

    <!-- 服务类 -->
    <Compile Include="Services\ImportService.cs" />
    <Compile Include="Services\ExportService.cs" />
    <Compile Include="Services\LoggingService.cs" />
    <Compile Include="Services\ComparisonDataExportService.cs" />

    <!-- 窗体类 -->
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MineralogicalForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MineralogicalForm.Designer.cs">
      <DependentUpon>MineralogicalForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\VisualizationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\VisualizationForm.Designer.cs">
      <DependentUpon>VisualizationForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ManualMappingForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ComparisonDataExportDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DataPreviewForm.cs">
      <SubType>Form</SubType>
    </Compile>

    <!-- 控件类 -->
    <Compile Include="Controls\MineralStackedBarChartControl.cs">
      <SubType>UserControl</SubType>
    </Compile>

    <!-- 主程序 -->
    <Compile Include="Program.cs" />
    <Compile Include="AppConfig.cs" />
  </ItemGroup>

  <!-- 资源文件 -->
  <ItemGroup>
    <EmbeddedResource Include="Controls\MineralStackedBarChartControl.resx" />
    <EmbeddedResource Include="Core\AlgorithmFormulaCal.resx" />
    <EmbeddedResource Include="Forms\LoginForm.resx" />
    <EmbeddedResource Include="Forms\DashboardForm.resx" />
    <EmbeddedResource Include="Forms\MineralogicalForm.resx" />
    <EmbeddedResource Include="Forms\VisualizationForm.resx" />
    <EmbeddedResource Include="Forms\ManualMappingForm.resx" />
  </ItemGroup>

  <!-- HTML/CSS文件 -->
  <ItemGroup>
    <None Include="Resources\index.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Resources\style.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Resources\mineralogical.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Resources\mineralogical.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
