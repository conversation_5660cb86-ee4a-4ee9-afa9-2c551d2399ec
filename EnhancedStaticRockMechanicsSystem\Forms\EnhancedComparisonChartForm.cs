using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using EnhancedStaticRockMechanicsSystem.Models;
using EnhancedStaticRockMechanicsSystem.Services;

namespace EnhancedStaticRockMechanicsSystem.Forms
{
    /// <summary>
    /// 增强的对比图窗体，支持图片显示
    /// </summary>
    public partial class EnhancedComparisonChartForm : Form
    {
        private Chart chartComparison;
        private Panel pnlImageViewer;
        private PictureBox picImageViewer;
        private ComboBox cmbImages;
        private Button btnToggleImageView;
        private Splitter splitterImageChart;
        private Button btnClose;
        private Button btnSaveImage;
        private Button btnSeparate;
        private Button btnRestore;
        private Label lblTitle;
        private Panel pnlControls;

        private List<string> associatedImages = new List<string>();
        private bool imageViewVisible = false;
        private bool isSeparated = false;
        private List<ComparisonDataSet> currentDataSets = new List<ComparisonDataSet>();

        public EnhancedComparisonChartForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
            InitializeImageViewer();
        }

        private void InitializeCustomComponents()
        {
            this.SuspendLayout();

            // 设置窗体属性
            this.Text = "增强版脆性指数对比图";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(33, 33, 33);

            // 创建标题标签
            lblTitle = new Label();
            lblTitle.Text = "增强版脆性指数计算方法对比图";
            lblTitle.Font = new Font("微软雅黑", 16F, FontStyle.Bold);
            lblTitle.ForeColor = Color.White;
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Height = 60;
            lblTitle.BackColor = Color.FromArgb(45, 45, 45);

            // 创建控制面板
            pnlControls = new Panel();
            pnlControls.Height = 60;
            pnlControls.Dock = DockStyle.Bottom;
            pnlControls.BackColor = Color.FromArgb(45, 45, 45);

            // 创建图表
            chartComparison = new Chart();
            chartComparison.Dock = DockStyle.Fill;
            chartComparison.BackColor = Color.FromArgb(33, 33, 33);

            // 配置图表区域
            ChartArea chartArea = new ChartArea("MainArea");
            ConfigureChartArea(chartArea);
            chartComparison.ChartAreas.Add(chartArea);

            // 创建图例
            Legend legend = new Legend("ComparisonLegend");
            legend.BackColor = Color.FromArgb(60, 60, 60);
            legend.ForeColor = Color.White;
            legend.Docking = Docking.Right;
            legend.Alignment = StringAlignment.Center;
            chartComparison.Legends.Add(legend);

            // 创建控制按钮
            CreateControlButtons();

            // 添加控件到窗体
            this.Controls.Add(chartComparison);
            this.Controls.Add(pnlControls);
            this.Controls.Add(lblTitle);

            this.ResumeLayout(false);
        }

        private void ConfigureChartArea(ChartArea chartArea)
        {
            chartArea.BackColor = Color.FromArgb(33, 33, 33);
            chartArea.BorderColor = Color.FromArgb(60, 60, 60);
            chartArea.BorderWidth = 1;

            // X轴配置（脆性指数）
            chartArea.AxisX.Title = "脆性指数 (%)";
            chartArea.AxisX.TitleFont = new Font("微软雅黑", 12F, FontStyle.Bold);
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.FromArgb(100, 100, 100);
            chartArea.AxisX.MajorGrid.LineColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisX.MajorGrid.LineDashStyle = ChartDashStyle.Dot;
            chartArea.AxisX.Minimum = 0;
            chartArea.AxisX.Maximum = 100;

            // Y轴配置（深度）
            chartArea.AxisY.Title = "深度 (m)";
            chartArea.AxisY.TitleFont = new Font("微软雅黑", 12F, FontStyle.Bold);
            chartArea.AxisY.TitleForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LineColor = Color.FromArgb(100, 100, 100);
            chartArea.AxisY.MajorGrid.LineColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dot;
            chartArea.AxisY.IsReversed = true; // 深度轴反转
        }

        private void CreateControlButtons()
        {
            btnClose = new Button
            {
                Text = "关闭",
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(10, 12)
            };
            btnClose.Click += (s, e) => this.Close();

            btnSaveImage = new Button
            {
                Text = "保存图片",
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(100, 12)
            };
            btnSaveImage.Click += BtnSaveImage_Click;

            btnSeparate = new Button
            {
                Text = "分隔显示",
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(190, 12)
            };
            btnSeparate.Click += BtnSeparate_Click;

            btnRestore = new Button
            {
                Text = "合并显示",
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(280, 12),
                Enabled = false
            };
            btnRestore.Click += BtnRestore_Click;

            pnlControls.Controls.AddRange(new Control[] {
                btnClose, btnSaveImage, btnSeparate, btnRestore
            });
        }

        private void InitializeImageViewer()
        {
            // 创建图片查看面板
            pnlImageViewer = new Panel
            {
                Dock = DockStyle.Right,
                Width = 400,
                BackColor = Color.FromArgb(45, 45, 45),
                Visible = false
            };

            // 创建图片选择下拉框
            cmbImages = new ComboBox
            {
                Dock = DockStyle.Top,
                Height = 30,
                DropDownStyle = ComboBoxStyle.DropDownList,
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White
            };
            cmbImages.SelectedIndexChanged += CmbImages_SelectedIndexChanged;

            // 创建图片显示控件
            picImageViewer = new PictureBox
            {
                Dock = DockStyle.Fill,
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.FromArgb(33, 33, 33)
            };

            // 创建分隔器
            splitterImageChart = new Splitter
            {
                Dock = DockStyle.Right,
                Width = 3,
                BackColor = Color.FromArgb(60, 60, 60),
                Visible = false
            };

            // 创建切换按钮
            btnToggleImageView = new Button
            {
                Text = "显示图片",
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(370, 12)
            };
            btnToggleImageView.Click += BtnToggleImageView_Click;

            // 添加控件
            pnlImageViewer.Controls.Add(picImageViewer);
            pnlImageViewer.Controls.Add(cmbImages);

            this.Controls.Add(pnlImageViewer);
            this.Controls.Add(splitterImageChart);

            // 添加按钮到控制面板
            pnlControls.Controls.Add(btnToggleImageView);
        }

        /// <summary>
        /// 加载对比数据
        /// </summary>
        public void LoadComparisonData(List<ComparisonDataSet> dataSets)
        {
            try
            {
                currentDataSets = dataSets ?? new List<ComparisonDataSet>();
                chartComparison.Series.Clear();

                if (currentDataSets.Count == 0)
                {
                    lblTitle.Text = "增强版脆性指数计算方法对比图 - 暂无数据";
                    return;
                }

                var colors = new[] { Color.Blue, Color.Red, Color.Green, Color.Orange, Color.Purple, Color.Brown };
                int colorIndex = 0;

                foreach (var dataSet in currentDataSets)
                {
                    if (dataSet.DataPoints.Count == 0) continue;

                    var series = new Series(dataSet.SystemName)
                    {
                        ChartType = SeriesChartType.Line,
                        Color = colors[colorIndex % colors.Length],
                        BorderWidth = 2,
                        MarkerStyle = MarkerStyle.Circle,
                        MarkerSize = 6,
                        MarkerColor = colors[colorIndex % colors.Length]
                    };

                    foreach (var point in dataSet.DataPoints)
                    {
                        if (point.IsValid())
                        {
                            series.Points.AddXY(point.BrittleIndex, point.MidDepth);
                        }
                    }

                    if (series.Points.Count > 0)
                    {
                        chartComparison.Series.Add(series);
                        colorIndex++;
                    }
                }

                // 加载关联图片
                LoadAssociatedImages(currentDataSets);

                // 更新标题
                int totalPoints = currentDataSets.Sum(ds => ds.DataPoints.Count);
                lblTitle.Text = $"增强版脆性指数计算方法对比图 - 已加载 {currentDataSets.Count} 个系统，共 {totalPoints} 个数据点";

                LoggingService.Instance.Info($"成功加载对比图数据: {currentDataSets.Count} 个系统");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"加载对比数据失败: {ex.Message}");
                MessageBox.Show($"加载对比数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadAssociatedImages(List<ComparisonDataSet> dataSets)
        {
            associatedImages.Clear();
            cmbImages.Items.Clear();

            foreach (var dataSet in dataSets)
            {
                associatedImages.AddRange(dataSet.AssociatedImages);
            }

            // 去重并排序
            associatedImages = associatedImages.Distinct().OrderBy(Path.GetFileName).ToList();

            // 填充下拉框
            foreach (var imagePath in associatedImages)
            {
                cmbImages.Items.Add($"{Path.GetFileName(imagePath)}");
            }

            // 更新按钮状态
            btnToggleImageView.Enabled = associatedImages.Count > 0;
            if (associatedImages.Count > 0)
            {
                btnToggleImageView.Text = $"显示图片({associatedImages.Count})";
            }
        }

        private void BtnToggleImageView_Click(object sender, EventArgs e)
        {
            imageViewVisible = !imageViewVisible;

            pnlImageViewer.Visible = imageViewVisible;
            splitterImageChart.Visible = imageViewVisible;

            btnToggleImageView.Text = imageViewVisible ? "隐藏图片" : $"显示图片({associatedImages.Count})";

            if (imageViewVisible && cmbImages.Items.Count > 0 && cmbImages.SelectedIndex == -1)
            {
                cmbImages.SelectedIndex = 0;
            }
        }

        private void CmbImages_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbImages.SelectedIndex >= 0 && cmbImages.SelectedIndex < associatedImages.Count)
            {
                try
                {
                    string imagePath = associatedImages[cmbImages.SelectedIndex];
                    if (File.Exists(imagePath))
                    {
                        using (var fs = new FileStream(imagePath, FileMode.Open, FileAccess.Read))
                        {
                            picImageViewer.Image?.Dispose();
                            picImageViewer.Image = Image.FromStream(fs);
                        }
                    }
                }
                catch (Exception ex)
                {
                    LoggingService.Instance.Warning($"加载图片失败: {ex.Message}");
                    MessageBox.Show($"加载图片失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnSaveImage_Click(object sender, EventArgs e)
        {
            try
            {
                using (var sfd = new SaveFileDialog())
                {
                    sfd.Filter = "PNG图片|*.png|JPEG图片|*.jpg|BMP图片|*.bmp";
                    sfd.DefaultExt = "png";
                    sfd.FileName = $"对比图_{DateTime.Now:yyyyMMdd_HHmmss}";

                    if (sfd.ShowDialog() == DialogResult.OK)
                    {
                        chartComparison.SaveImage(sfd.FileName, ChartImageFormat.Png);
                        MessageBox.Show("图片保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存图片失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSeparate_Click(object sender, EventArgs e)
        {
            // 实现分隔显示逻辑
            isSeparated = true;
            btnSeparate.Enabled = false;
            btnRestore.Enabled = true;

            // 这里可以实现分隔显示的具体逻辑
            MessageBox.Show("分隔显示功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnRestore_Click(object sender, EventArgs e)
        {
            // 实现合并显示逻辑
            isSeparated = false;
            btnSeparate.Enabled = true;
            btnRestore.Enabled = false;

            // 这里可以实现合并显示的具体逻辑
            MessageBox.Show("合并显示功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            // 清理资源
            picImageViewer.Image?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
