<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <AssemblyTitle>增强版静态岩石力学参数法系统</AssemblyTitle>
    <AssemblyDescription>基于静态岩石力学参数的脆性指数计算系统 - 增强版</AssemblyDescription>
    <AssemblyCompany>BritSystem</AssemblyCompany>
    <AssemblyProduct>增强版静态岩石力学参数法系统</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>EnhancedStaticRockMechanicsSystem.Program</StartupObject>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="NPOI" Version="2.6.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Windows.Forms.DataVisualization" Version="1.0.0-prerelease.20110.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\" />
    <Folder Include="SampleData\" />
    <Folder Include="Documentation\" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Forms\StaticRockMechanicsForm.resx">
      <DependentUpon>StaticRockMechanicsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BatchImportWizard.resx">
      <DependentUpon>BatchImportWizard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\EnhancedComparisonChartForm.resx">
      <DependentUpon>EnhancedComparisonChartForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\DashboardForm.resx">
      <DependentUpon>DashboardForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <None Include="README.md" />
    <None Include="使用说明.md" />
    <None Include="启动系统.bat" />
  </ItemGroup>

</Project>
