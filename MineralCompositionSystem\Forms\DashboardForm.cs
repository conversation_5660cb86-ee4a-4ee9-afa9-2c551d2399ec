using System;
using System.Windows.Forms;
using System.Drawing;

namespace MineralCompositionSystem.Forms
{
    public class DashboardForm : Form
    {
        private Label lblTitle;
        private Button btnMineralogical;
        private Button btnStaticRockMechanics;
        private Button btnExit;
        private string username;

        public DashboardForm(string username)
        {
            this.username = username;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            lblTitle = new Label();
            btnMineralogical = new Button();
            btnStaticRockMechanics = new Button();
            btnExit = new Button();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Font = new Font("微软雅黑", 16F, FontStyle.Bold);
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.Location = new Point(0, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Padding = new Padding(0, 10, 0, 0);
            lblTitle.Size = new Size(578, 73);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "煤系气储层脆性指数分析系统V1.0";
            lblTitle.TextAlign = ContentAlignment.TopCenter;
            //
            // btnMineralogical
            //
            btnMineralogical.BackColor = Color.FromArgb(50, 50, 50);
            btnMineralogical.FlatAppearance.BorderColor = Color.Cyan;
            btnMineralogical.FlatStyle = FlatStyle.Flat;
            btnMineralogical.Font = new Font("微软雅黑", 12F);
            btnMineralogical.ForeColor = Color.LightSkyBlue;
            btnMineralogical.Location = new Point(120, 100);
            btnMineralogical.Name = "btnMineralogical";
            btnMineralogical.Size = new Size(150, 100);
            btnMineralogical.TabIndex = 1;
            btnMineralogical.Text = "矿物组分法\r\n脆性指数分析";
            btnMineralogical.UseVisualStyleBackColor = false;
            btnMineralogical.Click += BtnMineralogical_Click;
            //
            // btnStaticRockMechanics
            //
            btnStaticRockMechanics.BackColor = Color.FromArgb(50, 50, 50);
            btnStaticRockMechanics.FlatAppearance.BorderColor = Color.Cyan;
            btnStaticRockMechanics.FlatStyle = FlatStyle.Flat;
            btnStaticRockMechanics.Font = new Font("微软雅黑", 12F);
            btnStaticRockMechanics.ForeColor = Color.LightSkyBlue;
            btnStaticRockMechanics.Location = new Point(300, 100);
            btnStaticRockMechanics.Name = "btnStaticRockMechanics";
            btnStaticRockMechanics.Size = new Size(150, 100);
            btnStaticRockMechanics.TabIndex = 2;
            btnStaticRockMechanics.Text = "静态岩石力学\r\n参数法分析";
            btnStaticRockMechanics.UseVisualStyleBackColor = false;
            btnStaticRockMechanics.Click += BtnStaticRockMechanics_Click;
            // 
            // btnExit
            // 
            btnExit.BackColor = Color.FromArgb(60, 60, 60);
            btnExit.FlatAppearance.BorderColor = Color.Cyan;
            btnExit.FlatStyle = FlatStyle.Flat;
            btnExit.Font = new Font("微软雅黑", 10F);
            btnExit.ForeColor = Color.White;
            btnExit.Location = new Point(235, 230);
            btnExit.Name = "btnExit";
            btnExit.Size = new Size(114, 40);
            btnExit.TabIndex = 3;
            btnExit.Text = "退出系统";
            btnExit.UseVisualStyleBackColor = false;
            btnExit.Click += BtnExit_Click;
            // 
            // DashboardForm
            //
            BackColor = Color.FromArgb(33, 33, 33);
            ClientSize = new Size(578, 300);
            Controls.Add(lblTitle);
            Controls.Add(btnMineralogical);
            Controls.Add(btnStaticRockMechanics);
            Controls.Add(btnExit);
            Name = "DashboardForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "煤系气储层脆性指数分析系统V1.0";
            ResumeLayout(false);
        }

        private void LoadData()
        {
            // 简化版本，不需要加载数据
        }

        private void BtnMineralogical_Click(object sender, EventArgs e)
        {
            // 打开矿物组分法窗体
            MineralogicalForm mineralogicalForm = new MineralogicalForm(username);
            this.Hide();
            DialogResult result = mineralogicalForm.ShowDialog();
            if (result == DialogResult.OK)
            {
                this.Show();
            }
            else
            {
                this.Close();
            }
        }

        private void BtnStaticRockMechanics_Click(object sender, EventArgs e)
        {
            try
            {
                // 启动独立的静态岩石力学参数法系统
                string exePath = System.IO.Path.Combine(Application.StartupPath, "..", "EnhancedStaticRockMechanicsSystem", "bin", "Debug", "net8.0-windows", "EnhancedStaticRockMechanicsSystem.exe");

                if (!System.IO.File.Exists(exePath))
                {
                    // 尝试Release版本
                    exePath = System.IO.Path.Combine(Application.StartupPath, "..", "EnhancedStaticRockMechanicsSystem", "bin", "Release", "net8.0-windows", "EnhancedStaticRockMechanicsSystem.exe");
                }

                if (System.IO.File.Exists(exePath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = exePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("静态岩石力学参数法系统未找到，请确保EnhancedStaticRockMechanicsSystem已正确编译。",
                                  "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动静态岩石力学参数法系统时出错：{ex.Message}",
                              "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnExit_Click(object sender, EventArgs e)
        {
            // 退出系统
            Application.Exit();
        }


    }
}
